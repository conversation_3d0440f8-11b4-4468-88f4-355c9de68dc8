{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_24e2925a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|images|fonts).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|images|fonts).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lUJu64f0BENfp9iP7T2/TsE5/VqtGlGCMzTGnjEa7hA=", "__NEXT_PREVIEW_MODE_ID": "f2649017d6ab68ecf475daa1829b73e2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0d99b14f81f26b9018a1b6d25391601b8b77a7d18996f10b77d3dcf22e4a7694", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5f524f17395004e178f5bdded44f4a6f9771b04120d1831823d537b35d8651fb"}}}, "sortedMiddleware": ["/"], "functions": {}}