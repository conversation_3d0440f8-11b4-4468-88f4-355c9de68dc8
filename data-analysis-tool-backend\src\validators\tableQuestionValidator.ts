import { z } from "zod";

/**
 * Column schema for creating table questions
 * Validates column structure and parent-child relationships
 */
const createColumnSchema = z.object({
  columnName: z.string().min(1, "Column name is required"),
  parentColumnId: z.number().int().positive().optional(),
});

/**
 * Column schema for updating table questions
 * Includes optional ID field for existing columns
 */
const updateColumnSchema = z.object({
  id: z.number().int().positive().optional(),
  columnName: z.string().min(1, "Column name is required"),
  parentColumnId: z.number().int().positive().optional(),
});

/**
 * Default value schema for cells in rows
 */
const defaultValueSchema = z.object({
  columnId: z.number().int().positive("Valid column ID is required"),
  value: z.string(),
  code: z.string().optional(),
});

/**
 * Row schema for creating table questions
 */
const createRowSchema = z.object({
  rowsName: z.string().min(1, "Row name is required"),
  defaultValues: z.array(defaultValueSchema).optional(),
});

/**
 * Row schema for updating table questions
 * Includes optional ID field for existing rows
 */
const updateRowSchema = z.object({
  id: z.number().int().positive().optional(),
  rowsName: z.string().min(1, "Row name is required"),
});

/**
 * Schema for creating table questions
 * Validates the entire table structure including parent-child relationships
 */
export const createTableQuestionSchema = z
  .object({
    label: z.string().min(1, "Table label is required"),
    projectId: z.number().int().positive("Valid project ID is required"),
    columns: z
      .array(createColumnSchema)
      .min(1, "At least one column is required"),
    rows: z.array(createRowSchema).optional().default([]),
  })
  .superRefine((data, ctx) => {
    // Validate parent-child relationships
    validateParentChildRelationships(data.columns, ctx);

    // Ensure at least one default row for dynamic functionality
    if (!data.rows || data.rows.length === 0) {
      data.rows = [{ rowsName: "1" }]; // Default row with S.No. 1
    }
  });

/**
 * Schema for updating table questions
 * Validates the entire table structure including parent-child relationships
 */
export const updateTableQuestionSchema = z
  .object({
    label: z.string().min(1, "Table label is required"),
    columns: z
      .array(updateColumnSchema)
      .min(1, "At least one column is required"),
    rows: z.array(updateRowSchema).optional().default([]),
    projectId: z.number().int().positive().optional(), // Make it optional since we'll get it from the existing question
  })
  .superRefine((data, ctx) => {
    // Validate parent-child relationships
    validateParentChildRelationships(data.columns, ctx);

    // Ensure at least one default row for dynamic functionality
    if (!data.rows || data.rows.length === 0) {
      data.rows = [{ rowsName: "1" }]; // Default row with S.No. 1
    }
  });

/**
 * Schema for saving cell values
 */
export const saveCellValuesSchema = z.object({
  questionId: z.number().int().positive("Valid question ID is required"),
  cellValues: z
    .array(
      z.object({
        columnId: z.number().int().positive("Valid column ID is required"),
        rowsId: z.number().int().positive("Valid row ID is required"),
        value: z.string(),
        code: z.string().optional(),
        isDefault: z.boolean().optional(),
      })
    )
    .min(1, "At least one cell value is required"),
});

/**
 * Schema for adding a new row to a table question
 */
export const addRowSchema = z.object({
  rowsName: z.string().min(1, "Row name is required"),
});

/**
 * Schema for dynamic table submission with runtime rows
 */
export const dynamicTableSubmissionSchema = z.object({
  questionId: z.number().int().positive("Valid question ID is required"),
  rows: z
    .array(
      z.object({
        rowsName: z.string().min(1, "Row name is required"),
        isNew: z.boolean().optional(), // Flag to indicate if this is a new row
      })
    )
    .min(1, "At least one row is required"),
  cellValues: z
    .array(
      z.object({
        columnId: z.number().int().positive("Valid column ID is required"),
        rowIndex: z.number().int().nonnegative("Valid row index is required"), // Use index instead of rowsId for new rows
        value: z.string(),
        code: z.string().optional(),
      })
    )
    .optional()
    .default([]),
});

/**
 * Validates parent-child relationships in column data
 *
 * Rules:
 * 1. A parent column must exist before it can be referenced
 * 2. A column cannot be its own parent
 * 3. A parent can have at most 2 children
 * 4. No circular references are allowed
 *
 * @param columns - Array of column objects
 * @param ctx - Zod refinement context
 */
function validateParentChildRelationships(
  columns: { id?: number; columnName: string; parentColumnId?: number }[],
  ctx: z.RefinementCtx
) {
  // Map to track parent-child relationships
  const parentChildMap = new Map<number, number[]>();

  // Map to track column positions for error messages
  const columnPositions = new Map<number, number>();

  // First pass: build the parent-child map and validate basic rules
  columns.forEach((column, index) => {
    const position = index + 1;

    // Store column position for error messages
    if (column.id) {
      columnPositions.set(column.id, position);
    }

    if (column.parentColumnId) {
      // Rule 2: A column cannot be its own parent
      if (column.id && column.parentColumnId === column.id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Column "${column.columnName}" (position ${position}) cannot be its own parent`,
          path: ["columns", index, "parentColumnId"],
        });
        return;
      }

      // Add child to parent's children list
      if (!parentChildMap.has(column.parentColumnId)) {
        parentChildMap.set(column.parentColumnId, [position]);
      } else {
        parentChildMap.get(column.parentColumnId)!.push(position);
      }
    }
  });

  // Second pass: validate parent existence and child count
  columns.forEach((column, index) => {
    const position = index + 1;

    if (column.parentColumnId) {
      // Rule 1: Parent column must exist
      const parentExists = columns.some((col, idx) => {
        // Check if parent exists by ID or by position
        return (
          (col.id && col.id === column.parentColumnId) ||
          idx + 1 === column.parentColumnId
        );
      });

      if (!parentExists) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Parent column with ID ${column.parentColumnId} does not exist`,
          path: ["columns", index, "parentColumnId"],
        });
      }

      // Rule 3: A parent can have at most 2 children
      const parentId = column.parentColumnId;
      const childCount = parentChildMap.get(parentId)?.length || 0;

      if (childCount > 2) {
        const parentPosition =
          columnPositions.get(parentId) ||
          columns.findIndex((col) => col.id === parentId) + 1;
        const parentName =
          columns.find((col) => col.id === parentId)?.columnName ||
          columns[parentPosition - 1]?.columnName ||
          `Parent ${parentId}`;

        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Parent column "${parentName}" (position ${parentPosition}) cannot have more than 2 child columns`,
          path: ["columns", index, "parentColumnId"],
        });
      }
    }
  });

  // Third pass: detect circular references
  const visited = new Set<number>();
  const recursionStack = new Set<number>();

  function detectCycle(columnId: number): boolean {
    if (recursionStack.has(columnId)) {
      return true; // Cycle detected
    }

    if (visited.has(columnId)) {
      return false; // Already visited, no cycle
    }

    visited.add(columnId);
    recursionStack.add(columnId);

    // Get children of this column
    const children = columns
      .filter((col) => col.parentColumnId === columnId)
      .map((col) => col.id)
      .filter((id): id is number => id !== undefined);

    for (const childId of children) {
      if (detectCycle(childId)) {
        return true;
      }
    }

    recursionStack.delete(columnId);
    return false;
  }

  // Check for cycles starting from each column
  columns.forEach((column, index) => {
    if (column.id && !visited.has(column.id)) {
      if (detectCycle(column.id)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Circular reference detected involving column "${column.columnName}"`,
          path: ["columns", index],
        });
      }
    }
  });
}

export type CreateTableQuestionInput = z.infer<
  typeof createTableQuestionSchema
>;

export type UpdateTableQuestionInput = z.infer<
  typeof updateTableQuestionSchema
>;

export type SaveCellValuesInput = z.infer<typeof saveCellValuesSchema>;

export type AddRowInput = z.infer<typeof addRowSchema>;

export type DynamicTableSubmissionInput = z.infer<
  typeof dynamicTableSubmissionSchema
>;
