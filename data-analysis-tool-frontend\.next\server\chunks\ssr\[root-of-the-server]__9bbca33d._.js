module.exports = {

"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/lib/encodeDecode.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "decode": (()=>decode),
    "encode": (()=>encode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hashids$2f$esm$2f$hashids$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/hashids/esm/hashids.js [app-ssr] (ecmascript)");
;
const salt = process.env.SALT || "rushan-salt";
const hashids = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hashids$2f$esm$2f$hashids$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](salt, 12);
const encode = (id)=>{
    return hashids.encode(id);
};
const decode = (hash)=>{
    const decodedNumberLike = hashids.decode(hash)[0];
    const decoded = typeof decodedNumberLike === "bigint" ? decodedNumberLike < Number.MAX_SAFE_INTEGER ? Number(decodedNumberLike) : null : typeof decodedNumberLike === "number" ? decodedNumberLike : null;
    return decoded;
};
;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/lib/axios.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
;
const axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:4000/api") || "http://localhost:4000/api",
    headers: {
        "Content-Type": "application/json"
    },
    withCredentials: true
});
// Add request interceptor to handle auth token
axiosInstance.interceptors.request.use((config)=>{
    // You can add auth token here if needed
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// Add response interceptor to handle errors
axiosInstance.interceptors.response.use((response)=>response, (error)=>{
    if (error.code === "ERR_NETWORK") {
        console.error("Network error - Please check if the backend server is running");
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = axiosInstance;
}}),
"[project]/lib/api/form-builder.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addQuestion": (()=>addQuestion),
    "deleteQuestion": (()=>deleteQuestion),
    "duplicateQuestion": (()=>duplicateQuestion),
    "fetchQuestionBlockQuestions": (()=>fetchQuestionBlockQuestions),
    "fetchQuestions": (()=>fetchQuestions),
    "fetchTemplateQuestions": (()=>fetchTemplateQuestions),
    "updateQuestion": (()=>updateQuestion),
    "updateQuestionPositions": (()=>updateQuestionPositions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-ssr] (ecmascript)");
;
const getQuestionsEndPoint = (contextType)=>{
    if (contextType === "project") return "/questions";
    else if (contextType === "template") return "/template-questions";
    else if (contextType === "questionBlock") return "/question-blocks";
    throw new Error("Unsupported context type");
};
const fetchQuestions = async ({ projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/questions/${projectId}`);
    return data.questions;
};
const fetchTemplateQuestions = async ({ templateId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/template-questions/${templateId}`);
    return data.questions;
};
const addQuestion = async ({ contextType, contextId, dataToSend, position })=>{
    // For question blocks, we don't need to include the contextId in the URL
    // The userId is taken from the authenticated user in the backend
    const url = contextType === "questionBlock" ? `${getQuestionsEndPoint(contextType)}` : `${getQuestionsEndPoint(contextType)}/${contextId}`;
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(url, {
        ...dataToSend,
        position: position || 1
    });
    return data;
};
const deleteQuestion = async ({ contextType, id, projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`);
    return data;
};
const duplicateQuestion = async ({ id, contextType, contextId })=>{
    // For question blocks, we don't need to send the contextId in the body
    // The userId is taken from the authenticated user in the backend
    const requestBody = contextType === "questionBlock" ? {} : contextType === "project" ? {
        projectId: contextId
    } : {
        templateId: contextId
    };
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`${getQuestionsEndPoint(contextType)}/duplicate/${id}?projectId=${contextId}`, requestBody);
    return data;
};
const updateQuestion = async ({ id, contextType, dataToSend, contextId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`, dataToSend);
    return data;
};
const fetchQuestionBlockQuestions = async ()=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/question-blocks`);
        return response.data.questions || [];
    } catch (error) {
        console.error("Error fetching question block questions:", error);
        throw error;
    }
};
const updateQuestionPositions = async ({ contextType, contextId, questionPositions })=>{
    // Only support position updates for projects currently
    if (contextType !== "project") {
        throw new Error("Question position updates are only supported for projects");
    }
    const url = `${getQuestionsEndPoint(contextType)}/positions?projectId=${contextId}`;
    const payload = {
        questionPositions
    };
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(url, payload);
        return data;
    } catch (error) {
        console.error("Update failed - Full error:", error);
        console.error("Update failed - Error details:", {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            message: error.message,
            config: {
                url: error.config?.url,
                method: error.config?.method,
                data: error.config?.data
            }
        });
        throw error;
    }
};
;
}}),
"[project]/lib/api/question-groups.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createQuestionGroup": (()=>createQuestionGroup),
    "deleteQuestionAndGroup": (()=>deleteQuestionAndGroup),
    "deleteQuestionGroup": (()=>deleteQuestionGroup),
    "fetchQuestionGroups": (()=>fetchQuestionGroups),
    "moveQuestionBetweenGroups": (()=>moveQuestionBetweenGroups),
    "removeQuestionFromGroup": (()=>removeQuestionFromGroup),
    "updateQuestionGroup": (()=>updateQuestionGroup)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-ssr] (ecmascript)");
;
const fetchQuestionGroups = async ({ projectId })=>{
    try {
        // Use the project endpoint to fetch the project with its question groups
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/projects/form/${projectId}`);
        console.log("Fetched project with question groups:", data);
        // Extract question groups from the project data
        const questionGroups = data.data?.project?.questionGroup || [];
        console.log("Extracted question groups:", questionGroups);
        return questionGroups;
    } catch (error) {
        console.error("Error fetching question groups from project endpoint:", error);
        // Fallback to direct question groups endpoint
        try {
            console.log("Trying fallback to direct question groups endpoint");
            const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/question-groups`, {
                projectId
            });
            console.log("Fallback response:", data);
            return data.data?.projectGroup || [];
        } catch (fallbackError) {
            console.error("Error in fallback fetch:", fallbackError);
            // Last resort: create a dummy group for debugging
            if ("TURBOPACK compile-time truthy", 1) {
                console.log("Creating dummy group for debugging");
                return [];
            }
            "TURBOPACK unreachable";
        }
    }
};
const createQuestionGroup = async ({ title, order, projectId, selectedQuestionIds })=>{
    try {
        console.log("Creating question group with data:", {
            title,
            order,
            projectId,
            selectedQuestionIds
        });
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/question-groups`, {
            title,
            order,
            projectId,
            selectedQuestionIds: selectedQuestionIds || []
        });
        console.log("Create question group response:", data);
        return data;
    } catch (error) {
        console.error("Error creating question group:", error);
        throw error;
    }
};
const updateQuestionGroup = async ({ id, title, order, selectedQuestionIds })=>{
    try {
        console.log("Updating question group with data:", {
            id,
            title,
            order,
            selectedQuestionIds
        });
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/question-groups`, {
            id,
            title,
            order,
            selectedQuestionIds
        });
        console.log("Update question group response:", data);
        return data;
    } catch (error) {
        console.error("Error updating question group:", error);
        throw error;
    }
};
const deleteQuestionGroup = async ({ id })=>{
    try {
        console.log(`Deleting question group with ID: ${id}`);
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/question-groups/${id}`);
        console.log("Delete question group response:", data);
        return data;
    } catch (error) {
        console.error("Error deleting question group:", error);
        throw error;
    }
};
const deleteQuestionAndGroup = async ({ id })=>{
    try {
        console.log(`Deleting question group and its questions with ID: ${id}`);
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/question-groups/group/question/${id}`);
        console.log("Delete question group and questions response:", data);
        return data;
    } catch (error) {
        console.error("Error deleting question group and questions:", error);
        throw error;
    }
};
const removeQuestionFromGroup = async ({ groupId, questionId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/question-groups/question/remove`, {
        groupId,
        questionId
    });
    return data;
};
const moveQuestionBetweenGroups = async ({ groupId, newGroupId, questionId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/question-groups/question/move`, {
        groupId,
        newGroupId,
        questionId
    });
    return data;
}; // Parent group functionality removed for simplicity
}}),
"[project]/lib/api/projects.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addProjectUser": (()=>addProjectUser),
    "archiveMultipleProjects": (()=>archiveMultipleProjects),
    "archiveProject": (()=>archiveProject),
    "checkUserExists": (()=>checkUserExists),
    "createAnswerSubmission": (()=>createAnswerSubmission),
    "createProjectFromTemplate": (()=>createProjectFromTemplate),
    "deleteMultipleProjects": (()=>deleteMultipleProjects),
    "deleteProject": (()=>deleteProject),
    "deployProject": (()=>deployProject),
    "fetchProjectById": (()=>fetchProjectById),
    "fetchProjectUsers": (()=>fetchProjectUsers),
    "fetchProjects": (()=>fetchProjects),
    "updateAnswerSubmission": (()=>updateAnswerSubmission)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-ssr] (ecmascript)");
;
const fetchProjectById = async ({ projectId })=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/projects/${projectId}`);
    return data.project;
};
const createProjectFromTemplate = async (dataToSend)=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/projects/from-template`, dataToSend);
    return data;
};
//Fetch all projects for the current user
const fetchProjects = async ()=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/projects`);
        return data.projects;
    } catch (error) {
        console.error("Error fetching projects:", error);
        throw error;
    }
};
// Delete project
const deleteProject = async (projectId)=>{
    const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/projects/delete/${projectId}`);
    return data;
};
// Delete multiple projects
const deleteMultipleProjects = async (projectIds)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/projects/delete-multiple`, {
            data: {
                projectIds
            }
        });
        return data;
    } catch (error) {
        console.error("Error deleting multiple projects:", error);
        throw error;
    }
};
//Archive project
const archiveProject = async (projectId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/projects/change-status/${projectId}`, {
            status: "archived"
        });
        return data;
    } catch (error) {
        console.error("Error archiving project:", error);
        throw error;
    }
};
//Deploy project
const deployProject = async (projectId, isUnarchive = false)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/projects/change-status/${projectId}`, {
            status: "deployed"
        });
        return data;
    } catch (error) {
        console.error("Error deploying project:", error);
        throw error;
    }
};
// Archive multiple projects
const archiveMultipleProjects = async (projectIds)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/projects/update-many-status`, {
            projectIds,
            status: "archived"
        });
        return data;
    } catch (error) {
        console.error("Error archiving multiple projects:", error);
        throw error;
    }
};
// Check if user exists by email
const checkUserExists = async (email)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/users/check-email`, {
            email
        });
        return data;
    } catch (error) {
        // Format error message consistently
        const errorMessage = typeof error.response?.data?.message === "object" ? JSON.stringify(error.response?.data?.message) : error.response?.data?.message || error.message || "Failed to check user";
        throw new Error(errorMessage);
    }
};
// Add user to project by email
const addProjectUser = async ({ projectId, email, permissions })=>{
    try {
        // First check if the user exists
        const userData = await checkUserExists(email);
        if (!userData || !userData.success) {
            throw new Error(userData?.message || "User not found");
        }
        // Now use the user ID to add them to the project
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/project-users`, {
            userId: userData.user.id,
            projectId,
            permission: permissions
        });
        return data;
    } catch (error) {
        console.error("Error adding user to project:", error);
        // Format error message as a string
        const errorMessage = typeof error.response?.data?.message === "object" ? JSON.stringify(error.response?.data?.message) : error.response?.data?.message || error.message || "Failed to add user";
        throw new Error(errorMessage);
    }
};
// Fetch all users for a specific project
const fetchProjectUsers = async (projectId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/project-users/${projectId}`);
        return data.data.AllUser;
    } catch (error) {
        console.error("Error fetching project users:", error);
        const errorMessage = typeof error.response?.data?.message === "object" ? JSON.stringify(error.response?.data?.message) : error.response?.data?.message || error.message || "Failed to fetch project users";
        throw new Error(errorMessage);
    }
};
// Create answer submission
const createAnswerSubmission = async (answers)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/answers/multiple`, answers);
        return data;
    } catch (error) {
        console.error("Error creating answer submission:", error);
        throw error;
    }
};
// Update answer submission
const updateAnswerSubmission = async (projectId, submissionId, answers)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/form-submissions/${projectId}/${submissionId}`, {
            answers
        });
        return data;
    } catch (error) {
        console.error("Error updating answer submission:", error);
        throw error;
    }
};
;
}}),
"[project]/components/general/Spinner.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const Spinner = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full flex items-center justify-center",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "size-8 rounded-full border-x-2 border-primary-500 animate-spin my-16"
        }, void 0, false, {
            fileName: "[project]/components/general/Spinner.tsx",
            lineNumber: 6,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/general/Spinner.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = Spinner;
}}),
"[project]/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "formatDate": (()=>formatDate)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(date, format = "short") {
    if (!date) return "";
    try {
        const dateObj = typeof date === "string" ? new Date(date) : date;
        // Return empty string if invalid date
        if (isNaN(dateObj.getTime())) return "";
        switch(format){
            case "short":
                return dateObj.toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "short",
                    day: "numeric"
                });
            case "long":
                return dateObj.toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit"
                });
            case "full":
                return dateObj.toLocaleDateString(undefined, {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                    weekday: "long",
                    hour: "2-digit",
                    minute: "2-digit",
                    second: "2-digit"
                });
            default:
                return dateObj.toLocaleDateString();
        }
    } catch (error) {
        console.error("Error formatting date:", error);
        return String(date);
    }
}
}}),
"[project]/components/ui/label.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Label": (()=>Label)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-label/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
const Label = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/label.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
Label.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$label$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
;
}}),
"[project]/components/ui/textarea.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex min-h-[80px] w-full rounded-md border border-gray-200 bg-neutral-100 px-3 py-2 text-sm shadow-sm placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-700 dark:bg-gray-900 dark:placeholder:text-gray-500", className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/textarea.tsx",
        lineNumber: 11,
        columnNumber: 7
    }, this);
});
Textarea.displayName = "Textarea";
;
}}),
"[project]/components/ui/checkbox.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Checkbox": (()=>Checkbox)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-checkbox/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check.js [app-ssr] (ecmascript) <export default as CheckIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
function Checkbox({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        "data-slot": "checkbox",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50", className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$checkbox$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Indicator"], {
            "data-slot": "checkbox-indicator",
            className: "flex items-center justify-center text-current transition-none",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckIcon$3e$__["CheckIcon"], {
                className: "size-3.5"
            }, void 0, false, {
                fileName: "[project]/components/ui/checkbox.tsx",
                lineNumber: 26,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/ui/checkbox.tsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/checkbox.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/components/ui/radio-group.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RadioGroup": (()=>RadioGroup),
    "RadioGroupItem": (()=>RadioGroupItem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-radio-group/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle.js [app-ssr] (ecmascript) <export default as Circle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const RadioGroup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("grid gap-2", className),
        ...props,
        ref: ref
    }, void 0, false, {
        fileName: "[project]/components/ui/radio-group.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
});
RadioGroup.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
const RadioGroupItem = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Item"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("aspect-square h-4 w-4 rounded-full border text-gray-900 shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-gray-950 disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus-visible:ring-gray-300", className),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Indicator"], {
            className: "flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Circle$3e$__["Circle"], {
                className: "h-2.5 w-2.5 fill-current text-current"
            }, void 0, false, {
                fileName: "[project]/components/ui/radio-group.tsx",
                lineNumber: 37,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/components/ui/radio-group.tsx",
            lineNumber: 36,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/radio-group.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
});
RadioGroupItem.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$radio$2d$group$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Item"].displayName;
;
}}),
"[project]/components/ui/table.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Table": (()=>Table),
    "TableBody": (()=>TableBody),
    "TableCaption": (()=>TableCaption),
    "TableCell": (()=>TableCell),
    "TableFooter": (()=>TableFooter),
    "TableHead": (()=>TableHead),
    "TableHeader": (()=>TableHeader),
    "TableRow": (()=>TableRow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
"use client";
;
;
function Table({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "table-container",
        className: "relative w-full overflow-x-auto",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
            "data-slot": "table",
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("w-full caption-bottom text-sm", className),
            ...props
        }, void 0, false, {
            fileName: "[project]/components/ui/table.tsx",
            lineNumber: 13,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
function TableHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
        "data-slot": "table-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("[&_tr]:border-b", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
function TableBody({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
        "data-slot": "table-body",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("[&_tr:last-child]:border-0", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
function TableFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tfoot", {
        "data-slot": "table-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
function TableRow({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
        "data-slot": "table-row",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 57,
        columnNumber: 5
    }, this);
}
function TableHead({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
        "data-slot": "table-head",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-foreground h-10 px-2 text-left align-middle font-medium neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
}
function TableCell({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
        "data-slot": "table-cell",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("p-2 align-middle neutral-100space-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 83,
        columnNumber: 5
    }, this);
}
function TableCaption({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("caption", {
        "data-slot": "table-caption",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground mt-4 text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/table.tsx",
        lineNumber: 99,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/components/ui/input.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-ssr] (ecmascript)");
;
;
function Input({ className, type, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
        type: type,
        "data-slot": "input",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", "focus-visible:border-ring focus-visible:ring-primary-500 focus-visible:ring-[1px]", "focus-visible:outline-none", "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/input.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/lib/api/table.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createTable": (()=>createTable),
    "deleteTable": (()=>deleteTable),
    "fetchTableStructure": (()=>fetchTableStructure),
    "saveCellValues": (()=>saveCellValues),
    "updateTable": (()=>updateTable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/axios.ts [app-ssr] (ecmascript)");
;
const fetchTableStructure = async (questionId)=>{
    try {
        console.log(`Fetching table structure for questionId: ${questionId}`);
        if (!questionId || isNaN(questionId)) {
            console.error("Invalid questionId:", questionId);
            throw new Error("Invalid question ID provided");
        }
        // First try the table-questions endpoint
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/table-questions/${questionId}`);
            console.log("Response from /table-questions/:", response.data);
            // Check if the response has the expected structure
            if (response.data && response.data.data && response.data.data.question) {
                console.log("Using question from data.data.question");
                return response.data.data.question;
            } else if (response.data && response.data.data) {
                console.log("Using data from data.data");
                return response.data.data;
            } else if (response.data && response.data.success) {
                console.log("Using data from response.data");
                return response.data;
            }
        } catch (err) {
            console.log("Error from /table-questions/ endpoint:", err);
        // Continue to try the next endpoint
        }
        // If that fails, try the questions endpoint
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/questions/${questionId}`);
            console.log("Response from /questions/:", response.data);
            if (response.data && response.data.data) {
                console.log("Using data from questions endpoint");
                return response.data.data;
            }
        } catch (err) {
            console.log("Error from /questions/ endpoint:", err);
        // Continue to try the next endpoint
        }
        // If that fails, try the tables endpoint as a last resort
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`/tables/${questionId}`);
            console.log("Response from /tables/:", response.data);
            if (response.data && response.data.data && response.data.data.question) {
                console.log("Using question from tables endpoint");
                return response.data.data.question;
            }
        } catch (err) {
            console.log("Error from /tables/ endpoint:", err);
        }
        // If all endpoints fail, throw an error
        console.error("All endpoints failed to return valid data");
        throw new Error("Failed to fetch table structure from any endpoint");
    } catch (error) {
        console.error("Error fetching table structure:", error);
        throw error;
    }
};
const saveCellValues = async (questionId, cellValues)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/table-questions/cells`, {
            questionId,
            cellValues
        });
        return data.data;
    } catch (error) {
        console.error("Error saving cell values:", error);
        throw error;
    }
};
const createTable = async (label, projectId, columns, rows)=>{
    try {
        // Validate inputs before sending to API
        if (!label || !label.trim()) {
            throw new Error("Table label is required");
        }
        if (!projectId || isNaN(projectId)) {
            throw new Error("Valid project ID is required");
        }
        if (!columns || !Array.isArray(columns) || columns.length === 0) {
            throw new Error("At least one column is required");
        }
        // Rows are now optional - validate only if provided
        if (rows && !Array.isArray(rows)) {
            throw new Error("Rows must be an array if provided");
        }
        // Ensure all columns have valid names
        const invalidColumns = columns.filter((col)=>!col.columnName || !col.columnName.trim());
        if (invalidColumns.length > 0) {
            throw new Error("All columns must have valid names");
        }
        // Ensure all rows have valid names if rows are provided
        if (rows) {
            const invalidRows = rows.filter((row)=>!row.rowsName || !row.rowsName.trim());
            if (invalidRows.length > 0) {
                throw new Error("All rows must have valid names");
            }
        }
        // The columns are already ordered correctly with parent-child relationships
        // We just need to pass them through to the backend
        console.log("Columns for create:", columns);
        // Create a clean version of the columns to send to the backend
        const cleanedColumns = columns.map((col)=>({
                columnName: col.columnName,
                parentColumnId: col.parentColumnId
            }));
        // Log the columns being sent to the backend
        console.log("Cleaned columns for backend:", cleanedColumns);
        // Log the rearranged columns
        console.log("Rearranged columns for backend:", cleanedColumns);
        // Ensure rows is always an array, even if empty
        const processedRows = rows || [];
        // Process rows to ensure default values are properly formatted
        const cleanedRows = processedRows.map((row)=>{
            const cleanedRow = {
                rowsName: row.rowsName.trim(),
                defaultValues: []
            };
            // Process default values if they exist
            if (row.defaultValues && row.defaultValues.length > 0) {
                console.log(`Processing ${row.defaultValues.length} default values for row ${row.rowsName}:`, row.defaultValues);
                // Filter out invalid default values but keep all valid ones
                cleanedRow.defaultValues = row.defaultValues.filter((dv)=>{
                    const isValid = dv.columnId && typeof dv.columnId === 'number' && dv.columnId > 0 && dv.value !== undefined && dv.value !== null && String(dv.value).trim() !== '';
                    if (!isValid) {
                        console.warn(`Filtering out invalid default value:`, dv);
                    }
                    return isValid;
                }).map((dv)=>{
                    const mappedValue = {
                        columnId: dv.columnId,
                        value: String(dv.value).trim(),
                        code: dv.code || String(dv.value).trim()
                    };
                    console.log(`Mapped default value:`, mappedValue);
                    return mappedValue;
                });
            }
            console.log(`Row ${row.rowsName} has ${cleanedRow.defaultValues.length} default values after cleaning:`, cleanedRow.defaultValues);
            return cleanedRow;
        });
        console.log("Creating table with data:", {
            label,
            projectId,
            columns: cleanedColumns,
            rows: cleanedRows
        });
        // Log the exact structure of rows with default values for debugging
        console.log("Rows with default values:");
        cleanedRows.forEach((row, index)=>{
            console.log(`Row ${index + 1} (${row.rowsName}):`);
            console.log(`  Default values: ${row.defaultValues.length}`);
            row.defaultValues.forEach((dv, i)=>{
                console.log(`    ${i + 1}. columnId: ${dv.columnId}, value: "${dv.value}", code: "${dv.code || dv.value}"`);
            });
        });
        // Use the table-questions endpoint which creates both a question and table structure
        // Note: The axios instance is configured with baseURL that includes /api, so we don't need to add it here
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post(`/table-questions`, {
            label,
            projectId,
            columns: cleanedColumns,
            rows: cleanedRows
        });
        console.log("Table created successfully:", data);
        if (!data || !data.success) {
            throw new Error(data?.message || "Failed to create table");
        }
        return data.data;
    } catch (error) {
        console.error("Error creating table:", error);
        // Enhance error message with response details if available
        if (error.response) {
            console.error("Response status:", error.response.status);
            console.error("Response data:", error.response.data);
            // If we have a more specific error message from the server, use it
            if (error.response.data && error.response.data.message) {
                error.message = error.response.data.message;
            }
        }
        throw error;
    }
};
const deleteTable = async (tableId)=>{
    try {
        const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].delete(`/table-questions/${tableId}`);
        return data;
    } catch (error) {
        console.error("Error deleting table:", error);
        throw error;
    }
};
const updateTable = async (tableId, label, columns, rows)=>{
    try {
        // Validate inputs before sending to API
        if (!label || !label.trim()) {
            throw new Error("Table label is required");
        }
        if (!tableId || isNaN(tableId)) {
            throw new Error("Valid table ID is required");
        }
        if (!columns || !Array.isArray(columns) || columns.length === 0) {
            throw new Error("At least one column is required");
        }
        // Rows are now optional - validate only if provided
        if (rows && !Array.isArray(rows)) {
            throw new Error("Rows must be an array if provided");
        }
        // Ensure all columns have valid names
        const invalidColumns = columns.filter((col)=>!col.columnName || !col.columnName.trim());
        if (invalidColumns.length > 0) {
            throw new Error("All columns must have valid names");
        }
        // Ensure all rows have valid names if rows are provided
        if (rows) {
            const invalidRows = rows.filter((row)=>!row.rowsName || !row.rowsName.trim());
            if (invalidRows.length > 0) {
                throw new Error("All rows must have valid names");
            }
        }
        // Validate parent-child relationships
        // Check for circular references or invalid parent IDs
        const columnIdMap = new Map();
        const columnPositionMap = new Map();
        // Map columns by ID and position
        columns.forEach((col, index)=>{
            if (col.id) {
                columnIdMap.set(col.id, col);
            }
            // Store 1-based position
            columnPositionMap.set(index + 1, col);
        });
        // Check each column with a parent
        for (const col of columns){
            if (col.parentColumnId) {
                // Ensure parentColumnId is a positive number
                if (col.parentColumnId <= 0) {
                    throw new Error(`Invalid parent column ID: ${col.parentColumnId}. Must be a positive number.`);
                }
                // Try to find parent by ID first
                let parentCol = columns.find((c)=>c.id === col.parentColumnId);
                // If not found by ID, try to find by position (for new columns)
                if (!parentCol && col.parentColumnId <= columns.length) {
                    parentCol = columnPositionMap.get(col.parentColumnId);
                    console.log(`Found parent by position ${col.parentColumnId}: ${parentCol?.columnName}`);
                }
                // If we still can't find the parent, it's an error
                if (!parentCol) {
                    throw new Error(`Parent column with ID/position ${col.parentColumnId} not found in the columns array.`);
                }
                // Check for circular references
                // If this column has a parent, and that parent also has a parent,
                // it would create a 3rd level, which we don't support
                if (parentCol.parentColumnId) {
                    throw new Error("Cannot create more than 2 levels of nested columns (parent → child → grandchild)");
                }
            }
        }
        // The columns are already ordered correctly with parent-child relationships
        // We just need to pass them through to the backend
        console.log("Columns for update:", columns);
        // Create a clean version of the columns to send to the backend
        const cleanedColumns = columns.map((col)=>{
            const cleanCol = {
                columnName: col.columnName.trim()
            };
            if (col.id) {
                cleanCol.id = col.id;
            }
            if (col.parentColumnId !== undefined) {
                cleanCol.parentColumnId = col.parentColumnId;
            }
            return cleanCol;
        });
        // Log the columns being sent to the backend
        console.log("Cleaned columns for backend:", cleanedColumns);
        console.log("Updating table with data:", {
            tableId,
            label,
            columns: cleanedColumns,
            rows
        });
        // Use the table-questions endpoint to update the table
        try {
            const { data } = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$axios$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch(`/table-questions/${tableId}`, {
                label: label.trim(),
                columns: cleanedColumns,
                rows: rows ? rows.map((row)=>({
                        ...row,
                        rowsName: row.rowsName.trim()
                    })) : []
            });
            console.log("Table updated successfully:", data);
            if (!data || !data.success) {
                throw new Error(data?.message || "Failed to update table");
            }
            return data.data;
        } catch (apiError) {
            console.error("API error updating table:", apiError);
            // Enhance error message with response details if available
            if (apiError.response) {
                console.error("Response status:", apiError.response.status);
                console.error("Response data:", apiError.response.data);
                // If we have a more specific error message from the server, use it
                if (apiError.response.data && apiError.response.data.message) {
                    throw new Error(apiError.response.data.message);
                }
            }
            // If we don't have a specific error message, throw the original error
            throw apiError;
        }
    } catch (error) {
        console.error("Error updating table:", error);
        // Rethrow the error with a clear message
        if (error.message) {
            throw new Error(`Failed to update table: ${error.message}`);
        } else {
            throw new Error("Failed to update table due to an unknown error");
        }
    }
};
}}),
"[project]/components/form-inputs/TableInput.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TableInput": (()=>TableInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/table.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$table$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/table.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-ssr] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-ssr] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$debounce$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash/debounce.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
const TableInput = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].memo(({ questionId, value, onChange, required = false })=>{
    // State declarations
    const [columns, setColumns] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [rows, setRows] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [cellValues, setCellValues] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [userInputValues, setUserInputValues] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Ref to track cellValues for responsive input
    const cellValuesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(cellValues);
    // Update ref whenever cellValues changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        cellValuesRef.current = cellValues;
    }, [
        cellValues
    ]);
    // Process columns to create a flat structure with parent-child relationships
    const processColumns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((tableData)=>{
        if (!tableData?.tableColumns) return [];
        const flattenedColumns = [];
        const parentColumns = tableData.tableColumns.filter((col)=>col.parentColumnId === null || col.parentColumnId === undefined);
        parentColumns.forEach((parentCol)=>{
            flattenedColumns.push(parentCol);
            const childColumns = parentCol.childColumns;
            if (childColumns && Array.isArray(childColumns) && childColumns.length > 0) {
                childColumns.forEach((childCol)=>{
                    flattenedColumns.push({
                        id: childCol.id,
                        columnName: childCol.columnName,
                        parentColumnId: childCol.parentColumnId
                    });
                });
            }
        });
        return flattenedColumns;
    }, []);
    // Memoize grouped columns to avoid recomputation on every render
    const groupedColumns = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!columns.length) {
            return {
                parentColumns: [],
                columnMap: new Map(),
                hasChildColumns: false
            };
        }
        const parentColumns = columns.filter((col)=>!col.parentColumnId);
        const columnMap = new Map();
        parentColumns.forEach((parentCol)=>{
            const childColumns = columns.filter((col)=>col.parentColumnId === parentCol.id);
            columnMap.set(parentCol.id, childColumns);
        });
        const hasChildColumns = parentColumns.some((p)=>(columnMap.get(p.id) ?? []).length > 0);
        return {
            parentColumns,
            columnMap,
            hasChildColumns
        };
    }, [
        columns
    ]);
    // Fetch table structure on mount or when questionId changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let isMounted = true;
        const loadTableStructure = async ()=>{
            try {
                setLoading(true);
                const tableData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$table$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchTableStructure"])(questionId);
                if (isMounted && tableData) {
                    console.log("Received table data:", tableData);
                    setColumns(processColumns(tableData));
                    setRows(tableData.tableRows || []);
                    // Process default values from rows and cell values
                    const defaultCellValues = {};
                    const defaultCellKeys = [];
                    // Check if we have cell values in the response
                    if (tableData.cellValues) {
                        console.log("Cell values found in response:", tableData.cellValues);
                        // Process cell values from the response
                        Object.entries(tableData.cellValues).forEach(([key, value])=>{
                            if (typeof value === "object" && value !== null && value.isDefault) {
                                defaultCellValues[key] = value.value;
                                defaultCellKeys.push(key);
                                console.log(`Loading default value from cellValues for ${key}: ${value.value}`);
                            } else if (typeof value === "string") {
                                defaultCellValues[key] = value;
                                console.log(`Loading cell value for ${key}: ${value}`);
                            }
                        });
                    }
                    // Also check if tableRows have defaultValues (for backward compatibility)
                    if (tableData.tableRows && tableData.tableRows.length > 0) {
                        tableData.tableRows.forEach((row)=>{
                            // Check if defaultValues exists on the row
                            // In some API responses, rows might not have the defaultValues property
                            if (row.defaultValues && Array.isArray(row.defaultValues) && row.defaultValues.length > 0) {
                                row.defaultValues.forEach((dv)=>{
                                    const key = `${dv.columnId}_${row.id}`;
                                    defaultCellValues[key] = dv.value;
                                    defaultCellKeys.push(key);
                                    console.log(`Loading default value from row for ${key}: ${dv.value}`);
                                });
                            } else {
                                console.log(`Row ${row.id} has no defaultValues property or it's empty`);
                            }
                        });
                    }
                    // Set default values in the cellValues state
                    if (Object.keys(defaultCellValues).length > 0) {
                        console.log("Setting cell values:", defaultCellValues);
                        console.log("Default cell keys:", defaultCellKeys);
                        // Force a re-render by creating a new object reference
                        const newCellValues = {
                            ...defaultCellValues
                        };
                        // Log each cell value for debugging
                        Object.entries(newCellValues).forEach(([key, value])=>{
                            console.log(`Setting cell value for ${key}: ${value}`);
                        });
                        // Set the cell values directly without merging with previous state
                        setCellValues(newCellValues);
                        // Wait a moment and then set the values again to ensure they're applied
                        setTimeout(()=>{
                            console.log("Re-applying cell values to ensure they are set");
                            setCellValues((prevValues)=>({
                                    ...prevValues
                                }));
                        }, 100);
                    } else {
                        console.warn("No default or cell values found for this table");
                    }
                } else if (isMounted) {
                    setError("Failed to load table structure");
                }
            } catch (err) {
                console.error("Error loading table structure:", err);
                if (isMounted) {
                    setError("Failed to load table structure");
                }
            } finally{
                if (isMounted) {
                    setLoading(false);
                }
            }
        };
        loadTableStructure();
        // Cleanup to prevent memory leaks
        return ()=>{
            isMounted = false;
        };
    }, [
        questionId,
        processColumns
    ]);
    // Handle initial value parsing and form reset
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (loading) return;
        const initialCellValues = {};
        let cellData = [];
        if (typeof value === "string" && value.trim()) {
            try {
                cellData = JSON.parse(value);
            } catch  {
                cellData = [];
            }
        } else if (Array.isArray(value)) {
            cellData = value;
        }
        cellData.forEach((cell)=>{
            initialCellValues[`${cell.columnId}_${cell.rowsId}`] = cell.value;
        });
        const isFormReset = !value || Array.isArray(value) && !value.length;
        if (isFormReset) {
            setUserInputValues({});
        } else if (Object.keys(initialCellValues).length) {
            setUserInputValues((prev)=>({
                    ...prev,
                    ...initialCellValues
                }));
        }
    }, [
        value,
        loading
    ]);
    // Debounced cell change handler to reduce rapid state updates
    const handleCellChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$debounce$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((columnId, rowId, newValue)=>{
        // Combine default values and user input values for submission
        const allValues = {
            ...cellValues,
            ...userInputValues
        };
        allValues[`${columnId}_${rowId}`] = newValue;
        const updatedCellValues = Object.entries(allValues).filter(([, value])=>value.trim()).map(([key, value])=>{
            const [colId, rowId] = key.split("_").map(Number);
            return {
                columnId: colId,
                rowsId: rowId,
                value
            };
        });
        onChange(updatedCellValues);
    }, 300, {
        leading: false,
        trailing: true
    }), [
        onChange,
        cellValues,
        userInputValues
    ]);
    // Handle input change with immediate local state update for responsiveness
    const handleInputChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((columnId, rowId, newValue)=>{
        // Update user input values (separate from default values)
        setUserInputValues((prev)=>({
                ...prev,
                [`${columnId}_${rowId}`]: newValue
            }));
        // Trigger debounced update
        handleCellChange(columnId, rowId, newValue);
    }, [
        handleCellChange
    ]);
    // Cleanup debounce on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            handleCellChange.cancel();
        };
    }, [
        handleCellChange
    ]);
    // Add a new row
    const handleAddRow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
        e.preventDefault();
        e.stopPropagation();
        setRows((prevRows)=>[
                ...prevRows,
                {
                    id: -(prevRows.length + 1),
                    rowsName: (prevRows.length + 1).toString()
                }
            ]);
    }, []);
    // Remove a row and clean up cell values
    const handleRemoveRow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((rowIndex)=>{
        if (rows.length <= 1) return;
        const rowToRemove = rows[rowIndex];
        setRows((prevRows)=>prevRows.filter((_, index)=>index !== rowIndex).map((row, idx)=>({
                    ...row,
                    rowsName: (idx + 1).toString()
                })));
        setUserInputValues((prevValues)=>{
            const newValues = {
                ...prevValues
            };
            Object.keys(newValues).forEach((key)=>{
                const [_, rowId] = key.split("_");
                if (rowId === rowToRemove.id.toString()) {
                    delete newValues[key];
                }
            });
            // Combine with default values for submission
            const allValues = {
                ...cellValues,
                ...newValues
            };
            const updatedCellValues = Object.entries(allValues).filter(([, value])=>value.trim()).map(([key, value])=>{
                const [colId, rowId] = key.split("_").map(Number);
                return {
                    columnId: colId,
                    rowsId: rowId,
                    value
                };
            });
            onChange(updatedCellValues);
            return newValues;
        });
    }, [
        rows,
        onChange,
        cellValues
    ]);
    // Memoize the table body to prevent re-rendering unchanged rows
    // Note: For large tables (>50 rows), consider using react-window for virtualization:
    // import { FixedSizeList } from "react-window";
    // <FixedSizeList height={400} itemCount={rows.length} itemSize={48} width="100%">
    //   {({ index, style }) => <Row index={index} style={style} />}
    // </FixedSizeList>
    const tableBody = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!rows.length) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableRow"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"], {
                        className: "border p-1 text-center font-medium bg-blue-50/50",
                        children: "Row 1"
                    }, void 0, false, {
                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                        lineNumber: 391,
                        columnNumber: 13
                    }, this),
                    groupedColumns.parentColumns.map((parentCol)=>{
                        const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                        if (!childColumns.length) {
                            const cellValue = cellValues[`${parentCol.id}_no_row`] || "";
                            const hasDefaultValue = cellValue.trim() !== "";
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"], {
                                className: "border p-1",
                                children: hasDefaultValue ? // Show default value as static text
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-2 bg-gray-50 border rounded w-full min-h-[38px] text-gray-800",
                                    children: cellValue
                                }, void 0, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 408,
                                    columnNumber: 23
                                }, this) : // Show input field for user entry
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                    value: userInputValues[`${parentCol.id}_no_row`] || "",
                                    onChange: (e)=>handleInputChange(parentCol.id, "no_row", e.target.value),
                                    placeholder: "Enter value",
                                    className: "w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500"
                                }, void 0, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 413,
                                    columnNumber: 23
                                }, this)
                            }, `cell-${parentCol.id}-no-row`, false, {
                                fileName: "[project]/components/form-inputs/TableInput.tsx",
                                lineNumber: 402,
                                columnNumber: 19
                            }, this);
                        }
                        return childColumns.map((childCol)=>{
                            const cellValue = cellValues[`${childCol.id}_no_row`] || "";
                            const hasDefaultValue = cellValue.trim() !== "";
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"], {
                                className: "border p-1",
                                children: hasDefaultValue ? // Show default value as static text
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-2 bg-gray-50 border rounded w-full min-h-[38px] text-gray-800",
                                    children: cellValue
                                }, void 0, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 440,
                                    columnNumber: 23
                                }, this) : // Show input field for user entry
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                    value: userInputValues[`${childCol.id}_no_row`] || "",
                                    onChange: (e)=>handleInputChange(childCol.id, "no_row", e.target.value),
                                    placeholder: "Enter value",
                                    className: "w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500"
                                }, void 0, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 445,
                                    columnNumber: 23
                                }, this)
                            }, `cell-${childCol.id}-no-row`, false, {
                                fileName: "[project]/components/form-inputs/TableInput.tsx",
                                lineNumber: 434,
                                columnNumber: 19
                            }, this);
                        });
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"], {
                        className: "border p-1 w-10"
                    }, void 0, false, {
                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                        lineNumber: 462,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/form-inputs/TableInput.tsx",
                lineNumber: 390,
                columnNumber: 11
            }, this);
        }
        return rows.map((row, rowIndex)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableRow"], {
                className: rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"], {
                        className: "border p-1 text-center font-medium bg-blue-50/50",
                        children: rowIndex + 1
                    }, void 0, false, {
                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                        lineNumber: 472,
                        columnNumber: 11
                    }, this),
                    groupedColumns.parentColumns.map((parentCol)=>{
                        const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                        if (!childColumns.length) {
                            // Make sure we're getting the cell value correctly
                            const cellKey = `${parentCol.id}_${row.id}`;
                            const cellValue = cellValues[cellKey] || "";
                            const hasDefaultValue = cellValue.trim() !== "";
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"], {
                                className: "border p-1",
                                children: hasDefaultValue ? // Show default value as static text
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-2 bg-gray-50 border rounded w-full min-h-[38px] text-gray-800",
                                    children: cellValue
                                }, void 0, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 491,
                                    columnNumber: 21
                                }, this) : // Show input field for user entry
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                    value: userInputValues[`${parentCol.id}_${row.id}`] || "",
                                    onChange: (e)=>handleInputChange(parentCol.id, row.id, e.target.value),
                                    placeholder: "Enter value",
                                    className: "w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500"
                                }, void 0, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 496,
                                    columnNumber: 21
                                }, this)
                            }, `cell-${parentCol.id}-${row.id}`, false, {
                                fileName: "[project]/components/form-inputs/TableInput.tsx",
                                lineNumber: 485,
                                columnNumber: 17
                            }, this);
                        }
                        return childColumns.map((childCol)=>{
                            const cellKey = `${childCol.id}_${row.id}`;
                            const cellValue = cellValues[cellKey] || "";
                            const hasDefaultValue = cellValue.trim() !== "";
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"], {
                                className: "border p-1",
                                children: hasDefaultValue ? // Show default value as static text
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-2 bg-gray-50 border rounded w-full min-h-[38px] text-gray-800",
                                    children: cellValue
                                }, void 0, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 520,
                                    columnNumber: 21
                                }, this) : // Show input field for user entry
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                    value: userInputValues[`${childCol.id}_${row.id}`] || "",
                                    onChange: (e)=>handleInputChange(childCol.id, row.id, e.target.value),
                                    placeholder: "Enter value",
                                    className: "w-full border-0 bg-transparent focus:ring-1 focus:ring-blue-500"
                                }, void 0, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 525,
                                    columnNumber: 21
                                }, this)
                            }, `cell-${childCol.id}-${row.id}`, false, {
                                fileName: "[project]/components/form-inputs/TableInput.tsx",
                                lineNumber: 514,
                                columnNumber: 17
                            }, this);
                        });
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableCell"], {
                        className: "border p-1 w-10",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            className: "text-red-500 hover:text-red-700",
                            onClick: ()=>handleRemoveRow(rowIndex),
                            disabled: rows.length <= 1,
                            "aria-label": "Delete Row",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                className: "w-4 h-4"
                            }, void 0, false, {
                                fileName: "[project]/components/form-inputs/TableInput.tsx",
                                lineNumber: 546,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/form-inputs/TableInput.tsx",
                            lineNumber: 539,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                        lineNumber: 538,
                        columnNumber: 11
                    }, this)
                ]
            }, row.id, true, {
                fileName: "[project]/components/form-inputs/TableInput.tsx",
                lineNumber: 468,
                columnNumber: 9
            }, this));
    }, [
        rows,
        cellValues,
        userInputValues,
        groupedColumns,
        required,
        handleInputChange,
        handleRemoveRow
    ]);
    const hasNoColumns = !columns.length;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "overflow-x-auto",
        children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-4 text-center",
            children: "Loading table..."
        }, void 0, false, {
            fileName: "[project]/components/form-inputs/TableInput.tsx",
            lineNumber: 566,
            columnNumber: 11
        }, this) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-4 text-center text-red-500",
            children: error
        }, void 0, false, {
            fileName: "[project]/components/form-inputs/TableInput.tsx",
            lineNumber: 568,
            columnNumber: 11
        }, this) : hasNoColumns ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "py-4 text-center text-amber-600",
            children: "This table has no columns defined. Please configure the table question first."
        }, void 0, false, {
            fileName: "[project]/components/form-inputs/TableInput.tsx",
            lineNumber: 570,
            columnNumber: 11
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Table"], {
                    className: "border-collapse",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableHeader"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableRow"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableHead"], {
                                            className: "text-center border bg-blue-50 font-medium",
                                            rowSpan: groupedColumns.hasChildColumns ? 2 : 1,
                                            children: "S.No."
                                        }, void 0, false, {
                                            fileName: "[project]/components/form-inputs/TableInput.tsx",
                                            lineNumber: 579,
                                            columnNumber: 19
                                        }, this),
                                        groupedColumns.parentColumns.map((parentCol)=>{
                                            const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                                            const colSpan = childColumns.length || 1;
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableHead"], {
                                                colSpan: colSpan,
                                                className: "text-center border bg-blue-50 font-medium",
                                                rowSpan: childColumns.length === 0 ? 2 : 1,
                                                children: parentCol.columnName
                                            }, parentCol.id, false, {
                                                fileName: "[project]/components/form-inputs/TableInput.tsx",
                                                lineNumber: 590,
                                                columnNumber: 23
                                            }, this);
                                        })
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 578,
                                    columnNumber: 17
                                }, this),
                                groupedColumns.hasChildColumns && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableRow"], {
                                    children: groupedColumns.parentColumns.map((parentCol)=>{
                                        const childColumns = groupedColumns.columnMap.get(parentCol.id) || [];
                                        if (!childColumns.length) return null;
                                        return childColumns.map((childCol)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableHead"], {
                                                className: "border bg-blue-50/50 text-sm",
                                                children: childCol.columnName
                                            }, childCol.id, false, {
                                                fileName: "[project]/components/form-inputs/TableInput.tsx",
                                                lineNumber: 608,
                                                columnNumber: 25
                                            }, this));
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                                    lineNumber: 602,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/form-inputs/TableInput.tsx",
                            lineNumber: 577,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableBody"], {
                            children: tableBody
                        }, void 0, false, {
                            fileName: "[project]/components/form-inputs/TableInput.tsx",
                            lineNumber: 619,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                    lineNumber: 576,
                    columnNumber: 13
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-2 flex justify-end",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: handleAddRow,
                        className: "border rounded px-3 py-1 text-sm flex items-center gap-1 hover:bg-blue-100",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/components/form-inputs/TableInput.tsx",
                                lineNumber: 627,
                                columnNumber: 17
                            }, this),
                            " Add Row"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/form-inputs/TableInput.tsx",
                        lineNumber: 622,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/form-inputs/TableInput.tsx",
                    lineNumber: 621,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true)
    }, void 0, false, {
        fileName: "[project]/components/form-inputs/TableInput.tsx",
        lineNumber: 564,
        columnNumber: 7
    }, this);
});
}}),
"[project]/app/form-test/[hashedId]/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>FormTestPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$encodeDecode$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/encodeDecode.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$form$2d$builder$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/form-builder.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$question$2d$groups$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/question-groups.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$projects$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/api/projects.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$general$2f$Spinner$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/general/Spinner.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/label.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/textarea.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$checkbox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/checkbox.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/radio-group.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/redux/slices/notificationSlice.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$TableInput$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/form-inputs/TableInput.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$debounce$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash/debounce.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function FormTestPage() {
    const dispatch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDispatch"])();
    const { hashedId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useParams"])();
    const hashedIdString = hashedId;
    const projectId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$encodeDecode$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["decode"])(hashedIdString);
    const [answers, setAnswers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [expandedGroups, setExpandedGroups] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const { data: questionsData, isLoading, isError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "questions",
            projectId
        ],
        queryFn: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$form$2d$builder$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchQuestions"])({
                projectId: projectId
            }),
        enabled: !!projectId
    });
    const { data: questionGroups = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "questionGroups",
            projectId
        ],
        queryFn: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$question$2d$groups$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchQuestionGroups"])({
                projectId: projectId
            }),
        enabled: !!projectId
    });
    const { data: projectData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey: [
            "project",
            projectId
        ],
        queryFn: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$projects$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fetchProjectById"])({
                projectId: projectId
            }),
        enabled: !!projectId
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (questionsData) {
            const initialAnswers = {};
            questionsData.forEach((question)=>{
                initialAnswers[question.id] = question.inputType === "selectmany" ? [] : "";
            });
            setAnswers(initialAnswers);
        }
    }, [
        questionsData
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (questionGroups.length > 0) {
            const initialExpandedState = {};
            questionGroups.forEach((group)=>{
                initialExpandedState[group.id] = true;
            });
            setExpandedGroups(initialExpandedState);
        }
    }, [
        questionGroups.length
    ]);
    const groupedQuestions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return questionGroups.reduce((acc, group)=>{
            acc[group.id] = questionsData?.filter((q)=>q.questionGroupId === group.id) || [];
            return acc;
        }, {});
    }, [
        questionGroups,
        questionsData
    ]);
    const ungroupedQuestions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return questionsData?.filter((q)=>q.questionGroupId === null || q.questionGroupId === undefined) || [];
    }, [
        questionsData
    ]);
    const unifiedFormItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const items = [];
        questionGroups.forEach((group)=>{
            const groupQuestions = questionsData?.filter((q)=>q.questionGroupId === group.id) || [];
            const minQuestionPosition = groupQuestions.length > 0 ? Math.min(...groupQuestions.map((q)=>q.position)) : group.order;
            items.push({
                type: "group",
                data: group,
                order: minQuestionPosition,
                originalPosition: minQuestionPosition
            });
        });
        ungroupedQuestions.forEach((question)=>{
            items.push({
                type: "question",
                data: question,
                order: question.position,
                originalPosition: question.position
            });
        });
        return items.sort((a, b)=>{
            if (a.order === b.order) {
                return (a.originalPosition || a.order) - (b.originalPosition || b.order);
            }
            return a.order - b.order;
        });
    }, [
        questionGroups,
        ungroupedQuestions,
        questionsData
    ]);
    const toggleGroupExpansion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((groupId)=>{
        setExpandedGroups((prev)=>({
                ...prev,
                [groupId]: !prev[groupId]
            }));
    }, []);
    const submitAnswersMutation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (answers)=>{
            const formattedAnswers = questionsData?.map((question)=>{
                const answerValue = answers[question.id];
                const isSelectMany = question.inputType === "selectmany";
                let questionOptionId;
                if (isSelectMany && Array.isArray(answerValue) && question.questionOptions) {
                    questionOptionId = answerValue.map((val)=>{
                        const option = question.questionOptions.find((opt)=>opt.label === val);
                        return option?.id;
                    }).filter((id)=>id !== undefined);
                } else if (question.inputType === "selectone" && answerValue && question.questionOptions) {
                    const option = question.questionOptions.find((opt)=>opt.label === answerValue);
                    questionOptionId = option?.id;
                }
                let formattedValue;
                if (isSelectMany) {
                    formattedValue = Array.isArray(answerValue) && answerValue.length > 0 ? answerValue.join(", ") : undefined;
                } else if (question.inputType === "number") {
                    formattedValue = answerValue ? Number(answerValue) : undefined;
                } else if (question.inputType === "date" || question.inputType === "dateandtime") {
                    formattedValue = answerValue || undefined;
                } else if (question.inputType === "table") {
                    if (Array.isArray(answerValue) && answerValue.length > 0) {
                        try {
                            const validatedCellValues = answerValue.map((cell)=>({
                                    columnId: Number(cell.columnId),
                                    rowsId: Number(cell.rowsId),
                                    value: String(cell.value || "")
                                }));
                            formattedValue = JSON.stringify(validatedCellValues);
                        } catch (err) {
                            console.error("Error formatting table data:", err);
                            formattedValue = undefined;
                        }
                    } else {
                        formattedValue = undefined;
                    }
                } else {
                    formattedValue = answerValue ? String(answerValue) : undefined;
                }
                return {
                    projectId: Number(projectId),
                    questionId: question.id,
                    answerType: String(question.inputType),
                    value: formattedValue,
                    questionOptionId,
                    isOtherOption: false
                };
            }) || [];
            return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$api$2f$projects$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createAnswerSubmission"])(formattedAnswers);
        },
        onSuccess: (data)=>{
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["showNotification"])({
                message: "Form submitted successfully",
                type: "success"
            }));
            setAnswers({});
            window.dispatchEvent(new Event("form-submitted"));
            localStorage.setItem("form_submitted", Date.now().toString());
        },
        onError: (error)=>{
            dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$redux$2f$slices$2f$notificationSlice$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["showNotification"])({
                message: "Failed to submit form. Please try again.",
                type: "error"
            }));
            console.error("Submission Error:", error);
        },
        onSettled: ()=>{
            setIsSubmitting(false);
        }
    });
    const handleTableInputChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$debounce$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((questionId, cellValues)=>{
        setAnswers((prev)=>({
                ...prev,
                [questionId]: cellValues
            }));
        setErrors((prev)=>({
                ...prev,
                [questionId]: ""
            }));
    }, 500, {
        leading: false,
        trailing: true
    }), []);
    const handleInputChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((questionId, value)=>{
        if (questionsData?.find((q)=>q.id === questionId)?.inputType === "table") {
            handleTableInputChange(questionId, value);
        } else {
            setAnswers((prev)=>({
                    ...prev,
                    [questionId]: value
                }));
            setErrors((prev)=>({
                    ...prev,
                    [questionId]: ""
                }));
        }
    }, [
        questionsData,
        handleTableInputChange
    ]);
    const validateForm = ()=>{
        const newErrors = {};
        let isValid = true;
        questionsData?.forEach((question)=>{
            if (question.isRequired) {
                const value = answers[question.id];
                if (typeof value === "string" && !value.trim() || Array.isArray(value) && value.length === 0 || value === undefined || value === null) {
                    newErrors[question.id] = `${question.label} is required`;
                    isValid = false;
                }
            }
        });
        setErrors(newErrors);
        return isValid;
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        if (!validateForm()) return;
        setIsSubmitting(true);
        submitAnswersMutation.mutate(answers);
    };
    const renderQuestionInput = (question)=>{
        const value = answers[question.id] ?? (question.inputType === "selectmany" ? [] : "");
        switch(question.inputType){
            case "text":
                if (question.hint?.includes("multiline")) {
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Textarea"], {
                        value: value,
                        onChange: (e)=>handleInputChange(question.id, e.target.value),
                        placeholder: question.placeholder || "Your answer",
                        required: question.isRequired
                    }, void 0, false, {
                        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                        lineNumber: 327,
                        columnNumber: 13
                    }, this);
                }
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                    className: "input-field w-full",
                    value: value,
                    onChange: (e)=>handleInputChange(question.id, e.target.value),
                    placeholder: question.placeholder || "Your answer",
                    required: question.isRequired
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 338,
                    columnNumber: 11
                }, this);
            case "number":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                    className: "input-field w-full",
                    type: "number",
                    value: value,
                    onChange: (e)=>handleInputChange(question.id, e.target.value),
                    placeholder: question.placeholder || "Your answer",
                    required: question.isRequired
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 349,
                    columnNumber: 11
                }, this);
            case "decimal":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                    className: "input-field w-full",
                    type: "decimal",
                    value: value,
                    onChange: (e)=>handleInputChange(question.id, e.target.value),
                    placeholder: question.placeholder || "Your answer",
                    required: question.isRequired
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 361,
                    columnNumber: 11
                }, this);
            case "selectone":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RadioGroup"], {
                    value: value,
                    onValueChange: (val)=>handleInputChange(question.id, val),
                    required: question.isRequired,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: question.questionOptions?.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$radio$2d$group$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["RadioGroupItem"], {
                                        value: option.label,
                                        id: `option-${option.id}`
                                    }, void 0, false, {
                                        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                        lineNumber: 381,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                        htmlFor: `option-${option.id}`,
                                        className: "cursor-pointer",
                                        children: option.label
                                    }, void 0, false, {
                                        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                        lineNumber: 385,
                                        columnNumber: 19
                                    }, this),
                                    option.sublabel && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-neutral-700 ml-4",
                                        children: `(${option.sublabel})`
                                    }, void 0, false, {
                                        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                        lineNumber: 392,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                lineNumber: 380,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                        lineNumber: 378,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 373,
                    columnNumber: 11
                }, this);
            case "selectmany":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-2",
                    children: question.questionOptions?.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$checkbox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Checkbox"], {
                                    id: `option-${option.id}`,
                                    checked: (value || []).includes(option.label),
                                    onCheckedChange: (checked)=>{
                                        const currentValues = value || [];
                                        const newValues = checked ? [
                                            ...currentValues,
                                            option.label
                                        ] : currentValues.filter((v)=>v !== option.label);
                                        handleInputChange(question.id, newValues);
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                    lineNumber: 407,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                                    htmlFor: `option-${option.id}`,
                                    className: "cursor-pointer",
                                    children: option.label
                                }, void 0, false, {
                                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                    lineNumber: 418,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, option.id, true, {
                            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                            lineNumber: 406,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 404,
                    columnNumber: 11
                }, this);
            case "date":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        className: "input-field w-full",
                        type: "date",
                        value: value,
                        onChange: (e)=>handleInputChange(question.id, e.target.value),
                        placeholder: question.placeholder || "Select date",
                        required: question.isRequired
                    }, void 0, false, {
                        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                        lineNumber: 432,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 431,
                    columnNumber: 11
                }, this);
            case "dateandtime":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        className: "input-field w-full",
                        type: "time",
                        value: value,
                        onChange: (e)=>handleInputChange(question.id, e.target.value),
                        placeholder: question.placeholder || "Select time",
                        required: question.isRequired
                    }, void 0, false, {
                        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                        lineNumber: 446,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 445,
                    columnNumber: 11
                }, this);
            case "table":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$form$2d$inputs$2f$TableInput$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TableInput"], {
                    questionId: question.id,
                    value: value,
                    onChange: (cellValues)=>handleInputChange(question.id, cellValues),
                    required: question.isRequired,
                    tableLabel: question.label
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 459,
                    columnNumber: 11
                }, this);
            default:
                return null;
        }
    };
    const renderQuestion = (question)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "border border-gray-200 dark:bg-gray-800 rounded-md p-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$label$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Label"], {
                            className: "text-base font-medium",
                            children: [
                                question.label,
                                question.isRequired && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-red-500 ml-1",
                                    children: "*"
                                }, void 0, false, {
                                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                    lineNumber: 483,
                                    columnNumber: 35
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                            lineNumber: 481,
                            columnNumber: 9
                        }, this),
                        question.hint && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-muted-foreground mt-1",
                            children: question.hint
                        }, void 0, false, {
                            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                            lineNumber: 486,
                            columnNumber: 11
                        }, this),
                        errors[question.id] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-red-500 mt-1",
                            children: errors[question.id]
                        }, void 0, false, {
                            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                            lineNumber: 489,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 480,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-2",
                    children: renderQuestionInput(question)
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 492,
                    columnNumber: 7
                }, this)
            ]
        }, question.id, true, {
            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
            lineNumber: 476,
            columnNumber: 5
        }, this);
    if (isLoading) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$general$2f$Spinner$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
        lineNumber: 496,
        columnNumber: 25
    }, this);
    if (isError || !questionsData) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
            className: "text-sm text-red-500",
            children: "Error loading form. Please try again."
        }, void 0, false, {
            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
            lineNumber: 499,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                    className: "text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700",
                    children: projectData?.name || "Test Form"
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 508,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: handleSubmit,
                    className: "p-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-6",
                        children: [
                            !questionsData || questionsData.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-center py-12",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-muted-foreground",
                                    children: "This form has no questions yet."
                                }, void 0, false, {
                                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                    lineNumber: 515,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                lineNumber: 514,
                                columnNumber: 15
                            }, this) : unifiedFormItems.map((item)=>{
                                if (item.type === "group") {
                                    const group = item.data;
                                    const groupQuestions = groupedQuestions[group.id] || [];
                                    const isExpanded = expandedGroups[group.id];
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",
                                                onClick: ()=>toggleGroupExpansion(group.id),
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center space-x-2",
                                                    children: [
                                                        isExpanded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                                            className: "h-5 w-5 text-gray-500"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                                            lineNumber: 537,
                                                            columnNumber: 29
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                                            className: "h-5 w-5 text-gray-500"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                                            lineNumber: 539,
                                                            columnNumber: 29
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-lg font-semibold text-gray-900 dark:text-gray-100",
                                                            children: group.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                                            lineNumber: 541,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-sm text-gray-500 dark:text-gray-400",
                                                            children: [
                                                                "(",
                                                                groupQuestions.length,
                                                                " question",
                                                                groupQuestions.length !== 1 ? "s" : "",
                                                                ")"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                                            lineNumber: 544,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                                    lineNumber: 535,
                                                    columnNumber: 25
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                                lineNumber: 531,
                                                columnNumber: 23
                                            }, this),
                                            isExpanded && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "p-4 space-y-4",
                                                children: groupQuestions.length > 0 ? groupQuestions.map((question)=>renderQuestion(question)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-center py-4 text-gray-500 dark:text-gray-400",
                                                    children: "No questions in this group."
                                                }, void 0, false, {
                                                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                                    lineNumber: 557,
                                                    columnNumber: 29
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                                lineNumber: 551,
                                                columnNumber: 25
                                            }, this)
                                        ]
                                    }, `group-${group.id}`, true, {
                                        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                        lineNumber: 527,
                                        columnNumber: 21
                                    }, this);
                                } else {
                                    const question = item.data;
                                    return renderQuestion(question);
                                }
                            }),
                            questionsData && questionsData.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-6 flex justify-end",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: "btn-primary",
                                    type: "submit",
                                    disabled: isSubmitting,
                                    children: isSubmitting ? "Submitting..." : "Submit Form"
                                }, void 0, false, {
                                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                    lineNumber: 573,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                                lineNumber: 572,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                        lineNumber: 512,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/form-test/[hashedId]/page.tsx",
                    lineNumber: 511,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/form-test/[hashedId]/page.tsx",
            lineNumber: 507,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/form-test/[hashedId]/page.tsx",
        lineNumber: 506,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9bbca33d._.js.map